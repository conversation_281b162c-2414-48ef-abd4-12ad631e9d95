const mongoose = require('mongoose');
const { Client: MinioClient } = require('minio');
require('dotenv').config();

async function testMongoDB() {
  try {
    console.log('🔌 测试MongoDB连接...');
    console.log('连接字符串:', process.env.MONGODB_URI);
    
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB 连接成功');
    
    // 测试基本操作
    const db = mongoose.connection.db;
    if (db) {
      const collections = await db.listCollections().toArray();
      console.log(`📊 现有集合数量: ${collections.length}`);
      collections.forEach(col => {
        console.log(`  - ${col.name}`);
      });
      
      // 测试products集合
      const productsCount = await db.collection('products').countDocuments();
      console.log(`📦 products集合文档数: ${productsCount}`);
      
      // 测试images集合
      const imagesCount = await db.collection('images').countDocuments();
      console.log(`🖼️ images集合文档数: ${imagesCount}`);
    }
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
  }
}

async function testMinIO() {
  try {
    console.log('\n🔌 测试MinIO连接...');
    console.log('MinIO配置:', {
      endpoint: process.env.MINIO_ENDPOINT,
      port: process.env.MINIO_PORT,
      accessKey: process.env.MINIO_ACCESS_KEY,
      bucket: process.env.MINIO_BUCKET
    });
    
    const minioClient = new MinioClient({
      endPoint: process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(process.env.MINIO_PORT || '9000'),
      useSSL: false,
      accessKey: process.env.MINIO_ACCESS_KEY || 'lcsm',
      secretKey: process.env.MINIO_SECRET_KEY || 'Sa2482047260'
    });

    // 测试连接
    const buckets = await minioClient.listBuckets();
    console.log('✅ MinIO 连接成功');
    console.log(`📁 现有存储桶数量: ${buckets.length}`);
    buckets.forEach(bucket => {
      console.log(`  - ${bucket.name} (创建时间: ${bucket.creationDate})`);
    });
    
    // 检查目标存储桶是否存在
    const bucketName = process.env.MINIO_BUCKET || 'product-images';
    const bucketExists = await minioClient.bucketExists(bucketName);
    console.log(`🪣 存储桶 '${bucketName}' 存在: ${bucketExists}`);
    
    if (bucketExists) {
      // 列出存储桶中的对象（前10个）
      const objectsStream = minioClient.listObjects(bucketName, '', true);
      const objects = [];
      let count = 0;
      
      for await (const obj of objectsStream) {
        objects.push(obj);
        count++;
        if (count >= 10) break; // 只显示前10个对象
      }
      
      console.log(`📄 存储桶中的对象数量（前10个）: ${objects.length}`);
      objects.forEach(obj => {
        console.log(`  - ${obj.name} (大小: ${obj.size} bytes)`);
      });
    }
    
  } catch (error) {
    console.error('❌ MinIO 连接失败:', error);
  }
}

async function main() {
  console.log('🚀 开始连接测试...\n');
  
  await testMongoDB();
  await testMinIO();
  
  console.log('\n✨ 连接测试完成！');
}

main().catch(console.error);
